import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:device_apps/device_apps.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/app_model.dart';

class AppRestrictionService {
  static SharedPreferences? _prefs;
  static const String _restrictedAppsKey = 'restricted_apps';
  static const String _lastReadTimeKey = 'last_read_time';
  static const String _readingSessionsKey = 'reading_sessions';
  static const String _appSettingsKey = 'app_settings';

  static Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    await _requestPermissions();
  }

  static Future<void> _requestPermissions() async {
    await Permission.packageUsageStats.request();
    await Permission.systemAlertWindow.request();
  }

  static Future<List<AppModel>> getInstalledApps() async {
    List<Application> apps = await DeviceApps.getInstalledApplications(
      includeAppIcons: true,
      includeSystemApps: false,
      onlyAppsWithLaunchIntent: true,
    );

    return apps.map((app) => AppModel(
      packageName: app.packageName,
      appName: app.appName,
      iconPath: app is ApplicationWithIcon ? app.icon.toString() : '',
    )).toList();
  }

  static Future<void> saveRestrictedApps(List<AppModel> apps) async {
    List<String> jsonList = apps.map((app) => jsonEncode(app.toJson())).toList();
    await _prefs?.setStringList(_restrictedAppsKey, jsonList);
  }

  static List<AppModel> getRestrictedApps() {
    List<String>? jsonList = _prefs?.getStringList(_restrictedAppsKey);
    if (jsonList == null) return [];
    
    return jsonList.map((json) => AppModel.fromJson(jsonDecode(json))).toList();
  }

  static bool canOpenApp(String packageName) {
    List<AppModel> restrictedApps = getRestrictedApps();
    AppModel? app = restrictedApps.firstWhere(
      (app) => app.packageName == packageName,
      orElse: () => AppModel(packageName: '', appName: '', iconPath: ''),
    );

    if (app.packageName.isEmpty || !app.isRestricted) return true;

    int? lastReadTime = _prefs?.getInt('${_lastReadTimeKey}_$packageName');
    if (lastReadTime == null) return false;

    DateTime lastRead = DateTime.fromMillisecondsSinceEpoch(lastReadTime);
    int validityHours = getAppValidityHours(packageName);
    return DateTime.now().difference(lastRead).inHours < validityHours;
  }

  static int getAppValidityHours(String packageName) {
    return _prefs?.getInt('${_appSettingsKey}_${packageName}_validity') ?? 24;
  }

  static Future<void> setAppValidityHours(String packageName, int hours) async {
    await _prefs?.setInt('${_appSettingsKey}_${packageName}_validity', hours);
  }

  static int getReadingTimeMinutes(String packageName) {
    return _prefs?.getInt('${_appSettingsKey}_${packageName}_reading_time') ?? 5;
  }

  static Future<void> setReadingTimeMinutes(String packageName, int minutes) async {
    await _prefs?.setInt('${_appSettingsKey}_${packageName}_reading_time', minutes);
  }

  static Future<void> markAppAsRead(String packageName) async {
    int currentTime = DateTime.now().millisecondsSinceEpoch;
    await _prefs?.setInt('${_lastReadTimeKey}_$packageName', currentTime);

    // Track reading session
    await _recordReadingSession(packageName, currentTime);
  }

  static Future<void> _recordReadingSession(String packageName, int timestamp) async {
    List<String> sessions = _prefs?.getStringList('${_readingSessionsKey}_$packageName') ?? [];
    sessions.add(timestamp.toString());

    // Keep only last 10 sessions to avoid excessive storage
    if (sessions.length > 10) {
      sessions = sessions.sublist(sessions.length - 10);
    }

    await _prefs?.setStringList('${_readingSessionsKey}_$packageName', sessions);
  }

  static List<DateTime> getReadingSessions(String packageName) {
    List<String> sessions = _prefs?.getStringList('${_readingSessionsKey}_$packageName') ?? [];
    return sessions.map((s) => DateTime.fromMillisecondsSinceEpoch(int.parse(s))).toList();
  }

  static DateTime? getLastReadTime(String packageName) {
    int? lastReadTime = _prefs?.getInt('${_lastReadTimeKey}_$packageName');
    return lastReadTime != null ? DateTime.fromMillisecondsSinceEpoch(lastReadTime) : null;
  }

  static String getTimeUntilNextAccess(String packageName) {
    DateTime? lastRead = getLastReadTime(packageName);
    if (lastRead == null) return 'Never accessed';

    int validityHours = getAppValidityHours(packageName);
    DateTime nextAccess = lastRead.add(Duration(hours: validityHours));
    Duration remaining = nextAccess.difference(DateTime.now());

    if (remaining.isNegative) return 'Available now';

    if (remaining.inHours > 0) {
      return '${remaining.inHours}h ${remaining.inMinutes % 60}m remaining';
    } else {
      return '${remaining.inMinutes}m remaining';
    }
  }
}