import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:device_apps/device_apps.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/app_model.dart';

class AppRestrictionService {
  static SharedPreferences? _prefs;
  static const String _restrictedAppsKey = 'restricted_apps';
  static const String _lastReadTimeKey = 'last_read_time';

  static Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    await _requestPermissions();
  }

  static Future<void> _requestPermissions() async {
    await Permission.packageUsageStats.request();
    await Permission.systemAlertWindow.request();
  }

  static Future<List<AppModel>> getInstalledApps() async {
    List<Application> apps = await DeviceApps.getInstalledApplications(
      includeAppIcons: true,
      includeSystemApps: false,
      onlyAppsWithLaunchIntent: true,
    );

    return apps.map((app) => AppModel(
      packageName: app.packageName,
      appName: app.appName,
      iconPath: app is ApplicationWithIcon ? app.icon.toString() : '',
    )).toList();
  }

  static Future<void> saveRestrictedApps(List<AppModel> apps) async {
    List<String> jsonList = apps.map((app) => jsonEncode(app.toJson())).toList();
    await _prefs?.setStringList(_restrictedAppsKey, jsonList);
  }

  static List<AppModel> getRestrictedApps() {
    List<String>? jsonList = _prefs?.getStringList(_restrictedAppsKey);
    if (jsonList == null) return [];
    
    return jsonList.map((json) => AppModel.fromJson(jsonDecode(json))).toList();
  }

  static bool canOpenApp(String packageName) {
    List<AppModel> restrictedApps = getRestrictedApps();
    AppModel? app = restrictedApps.firstWhere(
      (app) => app.packageName == packageName,
      orElse: () => AppModel(packageName: '', appName: '', iconPath: ''),
    );
    
    if (app.packageName.isEmpty || !app.isRestricted) return true;
    
    int? lastReadTime = _prefs?.getInt('${_lastReadTimeKey}_$packageName');
    if (lastReadTime == null) return false;
    
    DateTime lastRead = DateTime.fromMillisecondsSinceEpoch(lastReadTime);
    return DateTime.now().difference(lastRead).inHours < 24;
  }

  static Future<void> markAppAsRead(String packageName) async {
    await _prefs?.setInt(
      '${_lastReadTimeKey}_$packageName',
      DateTime.now().millisecondsSinceEpoch,
    );
  }
}