import 'package:flutter/material.dart';
import 'package:device_apps/device_apps.dart';
import '../services/app_restriction_service.dart';
import '../models/app_model.dart';

class AppSelectionScreen extends StatefulWidget {
  @override
  _AppSelectionScreenState createState() => _AppSelectionScreenState();
}

class _AppSelectionScreenState extends State<AppSelectionScreen> {
  List<AppModel> allApps = [];
  List<AppModel> restrictedApps = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadApps();
  }

  Future<void> _loadApps() async {
    allApps = await AppRestrictionService.getInstalledApps();
    restrictedApps = AppRestrictionService.getRestrictedApps();
    
    // Update restriction status
    for (AppModel app in allApps) {
      AppModel? restricted = restrictedApps.firstWhere(
        (r) => r.packageName == app.packageName,
        orElse: () => AppModel(packageName: '', appName: '', iconPath: ''),
      );
      app.isRestricted = restricted.packageName.isNotEmpty && restricted.isRestricted;
    }
    
    setState(() {
      isLoading = false;
    });
  }

  Future<void> _saveSelection() async {
    List<AppModel> selectedApps = allApps.where((app) => app.isRestricted).toList();
    await AppRestrictionService.saveRestrictedApps(selectedApps);
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Select Apps to Restrict'),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
        elevation: 2,
        actions: [
          TextButton.icon(
            onPressed: _saveSelection,
            icon: Icon(Icons.save, color: Colors.white),
            label: Text('SAVE', style: TextStyle(color: Colors.white, fontWeight: FontWeight.w600)),
          ),
        ],
      ),
      body: isLoading
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Loading installed apps...'),
                ],
              ),
            )
          : Column(
              children: [
                Container(
                  padding: EdgeInsets.all(16),
                  color: Colors.blue.shade50,
                  child: Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.blue),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Select apps that require 5-minute reading before opening',
                          style: TextStyle(
                            color: Colors.blue.shade800,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: ListView.builder(
                    padding: EdgeInsets.all(8),
                    itemCount: allApps.length,
                    itemBuilder: (context, index) {
                      AppModel app = allApps[index];
                      return Card(
                        margin: EdgeInsets.symmetric(vertical: 2, horizontal: 8),
                        child: CheckboxListTile(
                          contentPadding: EdgeInsets.all(12),
                          secondary: FutureBuilder<Application?>(
                            future: DeviceApps.getApp(app.packageName, true),
                            builder: (context, snapshot) {
                              if (snapshot.hasData && snapshot.data is ApplicationWithIcon) {
                                ApplicationWithIcon appWithIcon = snapshot.data as ApplicationWithIcon;
                                return Container(
                                  width: 40,
                                  height: 40,
                                  child: Image.memory(
                                    appWithIcon.icon,
                                    width: 40,
                                    height: 40,
                                  ),
                                );
                              }
                              return Container(
                                width: 40,
                                height: 40,
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade300,
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Icon(Icons.android, color: Colors.grey.shade600, size: 24),
                              );
                            },
                          ),
                          title: Text(
                            app.appName,
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 15,
                            ),
                          ),
                          subtitle: Text(
                            app.packageName,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                          value: app.isRestricted,
                          activeColor: Colors.blue,
                          onChanged: (bool? value) {
                            setState(() {
                              app.isRestricted = value ?? false;
                            });
                          },
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
    );
  }
}