import 'package:flutter/material.dart';
import '../services/app_restriction_service.dart';
import '../models/app_model.dart';

class AppSelectionScreen extends StatefulWidget {
  @override
  _AppSelectionScreenState createState() => _AppSelectionScreenState();
}

class _AppSelectionScreenState extends State<AppSelectionScreen> {
  List<AppModel> allApps = [];
  List<AppModel> restrictedApps = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadApps();
  }

  Future<void> _loadApps() async {
    allApps = await AppRestrictionService.getInstalledApps();
    restrictedApps = AppRestrictionService.getRestrictedApps();
    
    // Update restriction status
    for (AppModel app in allApps) {
      AppModel? restricted = restrictedApps.firstWhere(
        (r) => r.packageName == app.packageName,
        orElse: () => AppModel(packageName: '', appName: '', iconPath: ''),
      );
      app.isRestricted = restricted.packageName.isNotEmpty && restricted.isRestricted;
    }
    
    setState(() {
      isLoading = false;
    });
  }

  Future<void> _saveSelection() async {
    List<AppModel> selectedApps = allApps.where((app) => app.isRestricted).toList();
    await AppRestrictionService.saveRestrictedApps(selectedApps);
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Select Apps to Restrict'),
        actions: [
          TextButton(
            onPressed: _saveSelection,
            child: Text('SAVE', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
      body: isLoading
          ? Center(child: CircularProgressIndicator())
          : ListView.builder(
              itemCount: allApps.length,
              itemBuilder: (context, index) {
                AppModel app = allApps[index];
                return CheckboxListTile(
                  secondary: Icon(Icons.android),
                  title: Text(app.appName),
                  subtitle: Text(app.packageName),
                  value: app.isRestricted,
                  onChanged: (bool? value) {
                    setState(() {
                      app.isRestricted = value ?? false;
                    });
                  },
                );
              },
            ),
    );
  }
}