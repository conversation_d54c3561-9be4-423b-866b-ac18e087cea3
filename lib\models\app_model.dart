class AppModel {
  final String packageName;
  final String appName;
  final String iconPath;
  bool isRestricted;

  AppModel({
    required this.packageName,
    required this.appName,
    required this.iconPath,
    this.isRestricted = false,
  });

  Map<String, dynamic> toJson() => {
    'packageName': packageName,
    'appName': appName,
    'iconPath': iconPath,
    'isRestricted': isRestricted,
  };

  factory AppModel.fromJson(Map<String, dynamic> json) => AppModel(
    packageName: json['packageName'],
    appName: json['appName'],
    iconPath: json['iconPath'],
    isRestricted: json['isRestricted'] ?? false,
  );
}