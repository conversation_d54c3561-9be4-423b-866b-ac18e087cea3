@echo off
echo ========================================
echo PDF App Restrictor - Build Script
echo ========================================
echo.

echo Checking Flutter installation...
flutter doctor -v
if %errorlevel% neq 0 (
    echo ERROR: Flutter is not installed or not in PATH
    echo Please install Flutter first and add it to your PATH
    echo Visit: https://docs.flutter.dev/get-started/install/windows
    pause
    exit /b 1
)

echo.
echo Getting dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ERROR: Failed to get dependencies
    pause
    exit /b 1
)

echo.
echo Checking connected devices...
flutter devices
if %errorlevel% neq 0 (
    echo WARNING: No devices found. Make sure you have:
    echo 1. An Android device connected with USB debugging enabled, OR
    echo 2. An Android emulator running
    echo.
)

echo.
echo Do you want to:
echo 1. Run in debug mode (for testing)
echo 2. Build release APK
echo 3. Build and install APK
echo.
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" (
    echo.
    echo Running in debug mode...
    flutter run
) else if "%choice%"=="2" (
    echo.
    echo Building release APK...
    flutter build apk --release
    if %errorlevel% equ 0 (
        echo.
        echo ========================================
        echo BUILD SUCCESSFUL!
        echo ========================================
        echo APK location: build\app\outputs\flutter-apk\app-release.apk
        echo File size:
        dir "build\app\outputs\flutter-apk\app-release.apk" | find "app-release.apk"
        echo.
        echo You can now transfer this APK to your Android device and install it.
    ) else (
        echo ERROR: Build failed
    )
) else if "%choice%"=="3" (
    echo.
    echo Building release APK...
    flutter build apk --release
    if %errorlevel% equ 0 (
        echo.
        echo Installing APK on connected device...
        adb install build\app\outputs\flutter-apk\app-release.apk
        if %errorlevel% equ 0 (
            echo.
            echo ========================================
            echo BUILD AND INSTALL SUCCESSFUL!
            echo ========================================
            echo The app has been installed on your device.
        ) else (
            echo ERROR: Installation failed. Make sure:
            echo 1. Device is connected and USB debugging is enabled
            echo 2. ADB is working properly
            echo.
            echo You can manually install the APK from:
            echo build\app\outputs\flutter-apk\app-release.apk
        )
    ) else (
        echo ERROR: Build failed
    )
) else (
    echo Invalid choice. Please run the script again.
)

echo.
pause
