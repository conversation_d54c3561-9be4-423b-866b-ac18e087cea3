import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:android_intent_plus/android_intent.dart';
import '../services/app_restriction_service.dart';

class PDFReaderScreen extends StatefulWidget {
  final String appPackageName;
  final String appName;

  PDFReaderScreen({required this.appPackageName, required this.appName});

  @override
  _PDFReaderScreenState createState() => _PDFReaderScreenState();
}

class _PDFReaderScreenState extends State<PDFReaderScreen> {
  Timer? _timer;
  int _remainingSeconds = 300; // Default 5 minutes, will be updated
  int _totalSeconds = 300;
  bool _canOpenApp = false;
  String _readingContent = '';
  bool _isLoadingContent = true;
  ScrollController _scrollController = ScrollController();
  double _readingProgress = 0.0;
  bool _isPaused = false;

  @override
  void initState() {
    super.initState();
    _initializeReadingTime();
    _loadReadingContent();
    _startTimer();
    _scrollController.addListener(_updateReadingProgress);
  }

  void _initializeReadingTime() {
    int readingMinutes = AppRestrictionService.getReadingTimeMinutes(widget.appPackageName);
    _totalSeconds = readingMinutes * 60;
    _remainingSeconds = _totalSeconds;
  }

  Future<void> _loadReadingContent() async {
    try {
      String content = await rootBundle.loadString('assets/pdfs/sample_reading.txt');
      setState(() {
        _readingContent = content;
        _isLoadingContent = false;
      });
    } catch (e) {
      setState(() {
        _readingContent = 'Error loading reading content. Please check if the file exists.';
        _isLoadingContent = false;
      });
    }
  }

  void _updateReadingProgress() {
    if (_scrollController.hasClients) {
      double maxScroll = _scrollController.position.maxScrollExtent;
      double currentScroll = _scrollController.position.pixels;
      setState(() {
        _readingProgress = maxScroll > 0 ? (currentScroll / maxScroll).clamp(0.0, 1.0) : 0.0;
      });
    }
  }

  void _startTimer() {
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (!_isPaused) {
        setState(() {
          if (_remainingSeconds > 0) {
            _remainingSeconds--;
          } else {
            _canOpenApp = true;
            timer.cancel();
          }
        });
      }
    });
  }

  void _togglePause() {
    setState(() {
      _isPaused = !_isPaused;
    });
  }

  String _formatTime(int seconds) {
    int minutes = seconds ~/ 60;
    int remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  Future<void> _openApp() async {
    await AppRestrictionService.markAppAsRead(widget.appPackageName);
    
    final AndroidIntent intent = AndroidIntent(
      action: 'android.intent.action.MAIN',
      package: widget.appPackageName,
    );
    await intent.launch();
    Navigator.pop(context);
  }

  @override
  void dispose() {
    _timer?.cancel();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) {
        if (!didPop && !_canOpenApp) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Please complete the reading time')),
          );
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text('Read to unlock ${widget.appName}'),
          automaticallyImplyLeading: false,
          backgroundColor: _canOpenApp ? Colors.green : Colors.red,
        ),
        body: Column(
          children: [
            // Timer and Progress Header
            Container(
              padding: EdgeInsets.all(16),
              color: _canOpenApp ? Colors.green.shade100 : Colors.red.shade100,
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _canOpenApp ? 'Reading Complete!' : 'Time remaining: ${_formatTime(_remainingSeconds)}',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: _canOpenApp ? Colors.green.shade800 : Colors.red.shade800,
                              ),
                            ),
                            SizedBox(height: 8),
                            Text(
                              'Reading Progress: ${(_readingProgress * 100).toInt()}%',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey.shade700,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Row(
                        children: [
                          if (!_canOpenApp)
                            IconButton(
                              onPressed: _togglePause,
                              icon: Icon(_isPaused ? Icons.play_arrow : Icons.pause),
                              color: Colors.blue,
                            ),
                          if (_canOpenApp)
                            ElevatedButton(
                              onPressed: _openApp,
                              child: Text('Open ${widget.appName}'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green,
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                  SizedBox(height: 12),
                  // Progress bars
                  Column(
                    children: [
                      // Time progress
                      Row(
                        children: [
                          Text('Time: ', style: TextStyle(fontSize: 12)),
                          Expanded(
                            child: LinearProgressIndicator(
                              value: (_totalSeconds - _remainingSeconds) / _totalSeconds,
                              backgroundColor: Colors.grey.shade300,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                _canOpenApp ? Colors.green : Colors.blue,
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 8),
                      // Reading progress
                      Row(
                        children: [
                          Text('Read: ', style: TextStyle(fontSize: 12)),
                          Expanded(
                            child: LinearProgressIndicator(
                              value: _readingProgress,
                              backgroundColor: Colors.grey.shade300,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // Reading Content
            Expanded(
              child: _isLoadingContent
                  ? Center(child: CircularProgressIndicator())
                  : Container(
                      padding: EdgeInsets.all(16),
                      child: SingleChildScrollView(
                        controller: _scrollController,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (_isPaused)
                              Container(
                                width: double.infinity,
                                padding: EdgeInsets.all(12),
                                margin: EdgeInsets.only(bottom: 16),
                                decoration: BoxDecoration(
                                  color: Colors.orange.shade100,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(color: Colors.orange.shade300),
                                ),
                                child: Row(
                                  children: [
                                    Icon(Icons.pause_circle, color: Colors.orange),
                                    SizedBox(width: 8),
                                    Text(
                                      'Timer Paused - Continue reading to resume',
                                      style: TextStyle(
                                        color: Colors.orange.shade800,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            Text(
                              _readingContent,
                              style: TextStyle(
                                fontSize: 16,
                                height: 1.6,
                                color: Colors.black87,
                              ),
                            ),
                            SizedBox(height: 50), // Extra space at bottom
                          ],
                        ),
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }
}