import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:android_intent_plus/android_intent.dart';
import '../services/app_restriction_service.dart';

class PDFReaderScreen extends StatefulWidget {
  final String appPackageName;
  final String appName;

  PDFReaderScreen({required this.appPackageName, required this.appName});

  @override
  _PDFReaderScreenState createState() => _PDFReaderScreenState();
}

class _PDFReaderScreenState extends State<PDFReaderScreen> {
  Timer? _timer;
  int _remainingSeconds = 300; // 5 minutes
  bool _canOpenApp = false;

  @override
  void initState() {
    super.initState();
    _startTimer();
  }

  void _startTimer() {
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      setState(() {
        if (_remainingSeconds > 0) {
          _remainingSeconds--;
        } else {
          _canOpenApp = true;
          timer.cancel();
        }
      });
    });
  }

  String _formatTime(int seconds) {
    int minutes = seconds ~/ 60;
    int remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  Future<void> _openApp() async {
    await AppRestrictionService.markAppAsRead(widget.appPackageName);
    
    final AndroidIntent intent = AndroidIntent(
      action: 'android.intent.action.MAIN',
      package: widget.appPackageName,
    );
    await intent.launch();
    Navigator.pop(context);
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) {
        if (!didPop && !_canOpenApp) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Please complete the reading time')),
          );
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text('Read to unlock ${widget.appName}'),
          automaticallyImplyLeading: false,
          backgroundColor: _canOpenApp ? Colors.green : Colors.red,
        ),
        body: Column(
          children: [
            Container(
              padding: EdgeInsets.all(16),
              color: _canOpenApp ? Colors.green.shade100 : Colors.red.shade100,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _canOpenApp ? 'Reading Complete!' : 'Time remaining: ${_formatTime(_remainingSeconds)}',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: _canOpenApp ? Colors.green.shade800 : Colors.red.shade800,
                    ),
                  ),
                  if (_canOpenApp)
                    ElevatedButton(
                      onPressed: _openApp,
                      child: Text('Open ${widget.appName}'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                      ),
                    ),
                ],
              ),
            ),
            Expanded(
              child: PDFView(
                filePath: 'assets/pdfs/sample.pdf', // Add your PDF file here
                enableSwipe: true,
                swipeHorizontal: false,
                autoSpacing: false,
                pageFling: false,
                onRender: (pages) {
                  print("PDF rendered with $pages pages");
                },
                onError: (error) {
                  print("PDF Error: $error");
                },
                onPageError: (page, error) {
                  print("Page $page error: $error");
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}