import 'package:flutter/material.dart';
import 'package:device_apps/device_apps.dart';
import '../services/app_restriction_service.dart';
import '../models/app_model.dart';

class SettingsScreen extends StatefulWidget {
  @override
  _SettingsScreenState createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  List<AppModel> restrictedApps = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadRestrictedApps();
  }

  void _loadRestrictedApps() async {
    List<AppModel> apps = AppRestrictionService.getRestrictedApps()
        .where((app) => app.isRestricted)
        .toList();
    
    setState(() {
      restrictedApps = apps;
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Settings'),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
        elevation: 2,
      ),
      body: isLoading
          ? Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSectionHeader('App-Specific Settings'),
                  SizedBox(height: 8),
                  if (restrictedApps.isEmpty)
                    Card(
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Row(
                          children: [
                            Icon(Icons.info_outline, color: Colors.blue),
                            SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                'No restricted apps configured. Go to the main screen to select apps.',
                                style: TextStyle(color: Colors.blue.shade800),
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  else
                    ...restrictedApps.map((app) => _buildAppSettingsCard(app)).toList(),
                  
                  SizedBox(height: 24),
                  _buildSectionHeader('General Settings'),
                  SizedBox(height: 8),
                  _buildGeneralSettingsCard(),
                ],
              ),
            ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Colors.grey.shade800,
      ),
    );
  }

  Widget _buildAppSettingsCard(AppModel app) {
    return Card(
      margin: EdgeInsets.only(bottom: 8),
      child: ExpansionTile(
        leading: FutureBuilder<Application?>(
          future: DeviceApps.getApp(app.packageName, true),
          builder: (context, snapshot) {
            if (snapshot.hasData && snapshot.data is ApplicationWithIcon) {
              ApplicationWithIcon appWithIcon = snapshot.data as ApplicationWithIcon;
              return Container(
                width: 40,
                height: 40,
                child: Image.memory(appWithIcon.icon, width: 40, height: 40),
              );
            }
            return Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(Icons.android, color: Colors.grey.shade600, size: 24),
            );
          },
        ),
        title: Text(
          app.appName,
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Text(
          'Last read: ${_getLastReadText(app.packageName)}',
          style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
        ),
        children: [
          Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              children: [
                _buildReadingTimeSetting(app),
                SizedBox(height: 16),
                _buildValidityTimeSetting(app),
                SizedBox(height: 16),
                _buildReadingHistory(app),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReadingTimeSetting(AppModel app) {
    int currentMinutes = AppRestrictionService.getReadingTimeMinutes(app.packageName);
    
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Reading Time', style: TextStyle(fontWeight: FontWeight.w500)),
              Text('$currentMinutes minutes', style: TextStyle(color: Colors.grey.shade600)),
            ],
          ),
        ),
        DropdownButton<int>(
          value: currentMinutes,
          items: [1, 2, 3, 5, 10, 15, 20, 30].map((minutes) {
            return DropdownMenuItem(
              value: minutes,
              child: Text('$minutes min'),
            );
          }).toList(),
          onChanged: (value) async {
            if (value != null) {
              await AppRestrictionService.setReadingTimeMinutes(app.packageName, value);
              setState(() {});
            }
          },
        ),
      ],
    );
  }

  Widget _buildValidityTimeSetting(AppModel app) {
    int currentHours = AppRestrictionService.getAppValidityHours(app.packageName);
    
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Access Validity', style: TextStyle(fontWeight: FontWeight.w500)),
              Text('$currentHours hours', style: TextStyle(color: Colors.grey.shade600)),
            ],
          ),
        ),
        DropdownButton<int>(
          value: currentHours,
          items: [1, 2, 4, 6, 12, 24, 48].map((hours) {
            return DropdownMenuItem(
              value: hours,
              child: Text('$hours hrs'),
            );
          }).toList(),
          onChanged: (value) async {
            if (value != null) {
              await AppRestrictionService.setAppValidityHours(app.packageName, value);
              setState(() {});
            }
          },
        ),
      ],
    );
  }

  Widget _buildReadingHistory(AppModel app) {
    List<DateTime> sessions = AppRestrictionService.getReadingSessions(app.packageName);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Recent Reading Sessions', style: TextStyle(fontWeight: FontWeight.w500)),
        SizedBox(height: 8),
        if (sessions.isEmpty)
          Text('No reading sessions yet', style: TextStyle(color: Colors.grey.shade600))
        else
          Container(
            height: 100,
            child: ListView.builder(
              itemCount: sessions.length,
              itemBuilder: (context, index) {
                DateTime session = sessions[sessions.length - 1 - index];
                return Padding(
                  padding: EdgeInsets.symmetric(vertical: 2),
                  child: Text(
                    '${session.day}/${session.month}/${session.year} at ${session.hour.toString().padLeft(2, '0')}:${session.minute.toString().padLeft(2, '0')}',
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                  ),
                );
              },
            ),
          ),
      ],
    );
  }

  Widget _buildGeneralSettingsCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('About', style: TextStyle(fontWeight: FontWeight.w600, fontSize: 16)),
            SizedBox(height: 8),
            Text(
              'PDF App Restrictor helps you build mindful app usage habits by requiring a brief reading period before accessing selected apps.',
              style: TextStyle(color: Colors.grey.shade700),
            ),
            SizedBox(height: 16),
            Row(
              children: [
                Icon(Icons.info_outline, size: 16, color: Colors.blue),
                SizedBox(width: 8),
                Text('Version 1.0.0', style: TextStyle(color: Colors.blue)),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _getLastReadText(String packageName) {
    DateTime? lastRead = AppRestrictionService.getLastReadTime(packageName);
    if (lastRead == null) return 'Never';
    
    Duration diff = DateTime.now().difference(lastRead);
    if (diff.inDays > 0) return '${diff.inDays} days ago';
    if (diff.inHours > 0) return '${diff.inHours} hours ago';
    if (diff.inMinutes > 0) return '${diff.inMinutes} minutes ago';
    return 'Just now';
  }
}
