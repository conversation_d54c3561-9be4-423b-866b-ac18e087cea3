# PDF App Restrictor - Build Instructions

## Overview
This Flutter app restricts access to selected Android apps by requiring users to read content for 5 minutes before opening them. It's designed to promote mindful app usage and reduce digital distractions.

## Prerequisites

### 1. Install Flutter SDK
1. Download Flutter SDK from: https://docs.flutter.dev/get-started/install/windows
2. Extract to `C:\flutter` (or your preferred location)
3. Add `C:\flutter\bin` to your system PATH environment variable
4. Restart your command prompt/PowerShell

### 2. Install Android Studio
1. Download from: https://developer.android.com/studio
2. Install with default settings
3. Open Android Studio and install Android SDK (API level 21 or higher)
4. Install Android SDK Command-line Tools
5. Accept all Android licenses: `flutter doctor --android-licenses`

### 3. Setup Android Device/Emulator
**Option A: Physical Device**
1. Enable Developer Options on your Android device
2. Enable USB Debugging
3. Connect device via USB

**Option B: Android Emulator**
1. Open Android Studio
2. Go to Tools > AVD Manager
3. Create a new Virtual Device
4. Choose a device definition and system image (API 21+)
5. Start the emulator

## Build Instructions

### 1. Verify Flutter Installation
```bash
flutter doctor
```
Ensure all checkmarks are green (or at least Flutter and Android toolchain).

### 2. Get Dependencies
```bash
cd C:\Users\<USER>\Desktop\app2
flutter pub get
```

### 3. Check Connected Devices
```bash
flutter devices
```
You should see your connected device or running emulator.

### 4. Run in Debug Mode (Testing)
```bash
flutter run
```
This will install and run the app on your connected device/emulator for testing.

### 5. Build APK for Release
```bash
# Build APK (recommended for distribution)
flutter build apk --release

# Or build App Bundle (for Google Play Store)
flutter build appbundle --release
```

The APK will be created at: `build/app/outputs/flutter-apk/app-release.apk`

### 6. Install APK on Device
```bash
# If you have ADB installed
adb install build/app/outputs/flutter-apk/app-release.apk

# Or manually transfer the APK file to your device and install
```

## App Features

### Core Functionality
- **App Selection**: Choose which apps to restrict
- **5-Minute Reading Timer**: Configurable reading time per app
- **PDF/Text Reading**: Educational content during waiting period
- **Progress Tracking**: Visual indicators for reading and time progress
- **Persistent Settings**: App preferences saved locally

### User Interface
- **Home Screen**: Shows restricted apps and their status
- **App Selection Screen**: Choose apps to restrict with visual app icons
- **PDF Reader Screen**: Reading interface with timer and progress bars
- **Settings Screen**: Configure reading time and validity periods per app

### Technical Features
- **Local Storage**: Uses SharedPreferences for data persistence
- **App Icon Display**: Shows actual app icons using device_apps package
- **Intent Launching**: Opens restricted apps after reading completion
- **Permission Handling**: Requests necessary Android permissions

## Permissions Required

The app requests these Android permissions:
- `QUERY_ALL_PACKAGES`: To list installed apps
- `PACKAGE_USAGE_STATS`: To monitor app usage (optional)
- `SYSTEM_ALERT_WINDOW`: For overlay functionality (future feature)
- `FOREGROUND_SERVICE`: For background monitoring (future feature)

## Troubleshooting

### Common Issues

1. **Flutter not recognized**
   - Ensure Flutter is in your PATH
   - Restart command prompt after PATH changes

2. **Android licenses not accepted**
   - Run: `flutter doctor --android-licenses`
   - Accept all licenses

3. **No devices found**
   - Enable USB Debugging on your device
   - Check device connection with `adb devices`
   - Start Android emulator if using virtual device

4. **Build fails**
   - Run `flutter clean` then `flutter pub get`
   - Check that all dependencies are compatible

### Performance Tips
- Use release build for better performance: `flutter build apk --release`
- Test on physical device for accurate performance assessment
- Monitor memory usage with restricted apps

## App Architecture

### Key Components
- **Models**: `AppModel` for app data structure
- **Services**: `AppRestrictionService` for core logic and data persistence
- **Screens**: Home, App Selection, PDF Reader, Settings
- **Assets**: Reading content stored in `assets/pdfs/`

### Data Flow
1. User selects apps to restrict in App Selection Screen
2. Restricted apps are saved using AppRestrictionService
3. Home Screen displays restricted apps with current status
4. PDF Reader Screen enforces reading time before app access
5. Settings Screen allows customization of reading time and validity

## Future Enhancements
- Background app monitoring service
- Multiple PDF file support
- Usage statistics and analytics
- Custom reading content per app
- Notification system for reading reminders

## Support
For issues or questions, refer to:
- Flutter documentation: https://docs.flutter.dev/
- Android development: https://developer.android.com/docs
- Package documentation in pubspec.yaml
