import 'package:flutter/material.dart';
import 'screens/home_screen.dart';
import 'services/app_restriction_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await AppRestrictionService.initialize();
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'PDF App Restrictor',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        pageTransitionsTheme: const PageTransitionsTheme(
          builders: <TargetPlatform, PageTransitionsBuilder>{
            TargetPlatform.android: PredictiveBackPageTransitionsBuilder(),
          },
        ),
      ),
      home: HomeScreen(),
    );
  }
}