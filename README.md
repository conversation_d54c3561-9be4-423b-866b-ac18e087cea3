# PDF App Restrictor

A Flutter Android app that promotes mindful app usage by requiring users to read educational content for 5 minutes before accessing selected apps.

## Features

🔒 **App Restriction**: Select which apps require reading before access  
📖 **Educational Reading**: Engaging content about digital wellness and productivity  
⏱️ **Customizable Timer**: Configure reading time per app (1-30 minutes)  
📊 **Progress Tracking**: Visual indicators for reading and time progress  
⚙️ **Flexible Settings**: Customize validity periods and reading preferences  
📱 **Native Integration**: Seamlessly launches restricted apps after reading  

## Screenshots

*Screenshots will be available after building and testing the app*

## How It Works

1. **Select Apps**: Choose which apps you want to restrict
2. **Set Reading Time**: Configure how long you need to read (default: 5 minutes)
3. **Read Content**: When you try to open a restricted app, you'll be prompted to read
4. **Access Granted**: After completing the reading time, you can open the app
5. **Validity Period**: Apps remain accessible for a configured period (default: 24 hours)

## Installation

### Quick Start
1. Download and install Flutter SDK
2. Clone this repository
3. Run `build_apk.bat` for automated build process
4. Install the generated APK on your Android device

### Detailed Instructions
See [BUILD_INSTRUCTIONS.md](BUILD_INSTRUCTIONS.md) for complete setup and build instructions.

## Requirements

- **Android**: API level 21 (Android 5.0) or higher
- **Flutter**: 3.0 or higher
- **Development**: Android Studio with Android SDK

## Permissions

- `QUERY_ALL_PACKAGES`: To list installed apps
- `PACKAGE_USAGE_STATS`: For usage monitoring (optional)
- `SYSTEM_ALERT_WINDOW`: For overlay features (future)

## App Structure

```
lib/
├── main.dart                 # App entry point
├── models/
│   └── app_model.dart       # App data model
├── screens/
│   ├── home_screen.dart     # Main screen showing restricted apps
│   ├── app_selection_screen.dart  # Choose apps to restrict
│   ├── pdf_reader_screen.dart     # Reading interface with timer
│   └── settings_screen.dart       # Configuration options
└── services/
    └── app_restriction_service.dart  # Core logic and data persistence

assets/
└── pdfs/
    ├── sample_reading.txt          # Default reading content
    └── productivity_tips.txt       # Additional educational content
```

## Key Dependencies

- `flutter_pdfview`: PDF viewing capabilities
- `shared_preferences`: Local data storage
- `device_apps`: Access to installed apps and icons
- `android_intent_plus`: Launch apps after reading
- `permission_handler`: Handle Android permissions

## Usage Tips

### For Users
- Start with shorter reading times (1-2 minutes) and gradually increase
- Use the app during work hours to maintain focus
- Customize validity periods based on your usage patterns
- Read the educational content mindfully for maximum benefit

### For Developers
- The app uses SharedPreferences for local data storage
- App icons are loaded dynamically using the device_apps package
- Timer functionality includes pause/resume capabilities
- Settings are configurable per app for flexibility

## Contributing

This is a personal productivity app, but suggestions and improvements are welcome:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly on Android devices
5. Submit a pull request

## Future Enhancements

- [ ] Background app monitoring service
- [ ] Multiple PDF file support per app
- [ ] Usage statistics and analytics
- [ ] Custom reading content upload
- [ ] Notification reminders
- [ ] Widget for quick access
- [ ] Export/import settings

## Troubleshooting

**App not building?**
- Run `flutter doctor` to check your setup
- Ensure all Android licenses are accepted
- Try `flutter clean` and `flutter pub get`

**Apps not launching?**
- Check that the target app is still installed
- Verify Android intent permissions
- Test with different apps

**Timer not working?**
- Ensure the app has necessary permissions
- Check device battery optimization settings
- Test in debug mode for detailed logs

## License

This project is for educational and personal use. Feel free to modify and adapt for your needs.

## Support

For setup issues, refer to:
- [BUILD_INSTRUCTIONS.md](BUILD_INSTRUCTIONS.md)
- [Flutter Documentation](https://docs.flutter.dev/)
- [Android Developer Guide](https://developer.android.com/docs)

---

**Remember**: The goal is mindful technology use, not complete avoidance. Use this tool to develop better digital habits and improve your focus and productivity.
