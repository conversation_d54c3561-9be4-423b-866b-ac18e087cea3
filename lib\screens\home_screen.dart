import 'package:flutter/material.dart';
import '../services/app_restriction_service.dart';
import '../models/app_model.dart';
import 'app_selection_screen.dart';
import 'pdf_reader_screen.dart';

class HomeScreen extends StatefulWidget {
  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  List<AppModel> restrictedApps = [];

  @override
  void initState() {
    super.initState();
    _loadRestrictedApps();
  }

  void _loadRestrictedApps() {
    setState(() {
      restrictedApps = AppRestrictionService.getRestrictedApps()
          .where((app) => app.isRestricted)
          .toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('PDF App Restrictor'),
        actions: [
          IconButton(
            icon: Icon(Icons.settings),
            onPressed: () async {
              await Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => AppSelectionScreen()),
              );
              _loadRestrictedApps();
            },
          ),
        ],
      ),
      body: restrictedApps.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.apps, size: 64, color: Colors.grey),
                  SizedBox(height: 16),
                  Text('No restricted apps selected'),
                  SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () async {
                      await Navigator.push(
                        context,
                        MaterialPageRoute(builder: (context) => AppSelectionScreen()),
                      );
                      _loadRestrictedApps();
                    },
                    child: Text('Select Apps to Restrict'),
                  ),
                ],
              ),
            )
          : ListView.builder(
              itemCount: restrictedApps.length,
              itemBuilder: (context, index) {
                AppModel app = restrictedApps[index];
                bool canOpen = AppRestrictionService.canOpenApp(app.packageName);
                
                return ListTile(
                  leading: Icon(Icons.android),
                  title: Text(app.appName),
                  subtitle: Text(canOpen ? 'Available' : 'Requires reading'),
                  trailing: canOpen
                      ? Icon(Icons.check_circle, color: Colors.green)
                      : ElevatedButton(
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => PDFReaderScreen(
                                  appPackageName: app.packageName,
                                  appName: app.appName,
                                ),
                              ),
                            );
                          },
                          child: Text('Read PDF'),
                        ),
                );
              },
            ),
    );
  }
}