import 'package:flutter/material.dart';
import 'package:device_apps/device_apps.dart';
import '../services/app_restriction_service.dart';
import '../models/app_model.dart';
import 'app_selection_screen.dart';
import 'pdf_reader_screen.dart';
import 'settings_screen.dart';

class HomeScreen extends StatefulWidget {
  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  List<AppModel> restrictedApps = [];

  @override
  void initState() {
    super.initState();
    _loadRestrictedApps();
  }

  void _loadRestrictedApps() {
    setState(() {
      restrictedApps = AppRestrictionService.getRestrictedApps()
          .where((app) => app.isRestricted)
          .toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('PDF App Restrictor'),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
        elevation: 2,
        actions: [
          PopupMenuButton<String>(
            icon: Icon(Icons.more_vert),
            onSelected: (value) async {
              if (value == 'select_apps') {
                await Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => AppSelectionScreen()),
                );
                _loadRestrictedApps();
              } else if (value == 'settings') {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => SettingsScreen()),
                );
              }
            },
            itemBuilder: (BuildContext context) => [
              PopupMenuItem<String>(
                value: 'select_apps',
                child: Row(
                  children: [
                    Icon(Icons.apps, color: Colors.grey.shade700),
                    SizedBox(width: 8),
                    Text('Select Apps'),
                  ],
                ),
              ),
              PopupMenuItem<String>(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings, color: Colors.grey.shade700),
                    SizedBox(width: 8),
                    Text('Settings'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: restrictedApps.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.apps, size: 64, color: Colors.grey),
                  SizedBox(height: 16),
                  Text('No restricted apps selected'),
                  SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () async {
                      await Navigator.push(
                        context,
                        MaterialPageRoute(builder: (context) => AppSelectionScreen()),
                      );
                      _loadRestrictedApps();
                    },
                    child: Text('Select Apps to Restrict'),
                  ),
                ],
              ),
            )
          : ListView.builder(
              padding: EdgeInsets.all(8),
              itemCount: restrictedApps.length,
              itemBuilder: (context, index) {
                AppModel app = restrictedApps[index];
                bool canOpen = AppRestrictionService.canOpenApp(app.packageName);

                return Card(
                  margin: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                  elevation: 2,
                  child: ListTile(
                    contentPadding: EdgeInsets.all(12),
                    leading: FutureBuilder<Application?>(
                      future: DeviceApps.getApp(app.packageName, true),
                      builder: (context, snapshot) {
                        if (snapshot.hasData && snapshot.data is ApplicationWithIcon) {
                          ApplicationWithIcon appWithIcon = snapshot.data as ApplicationWithIcon;
                          return Container(
                            width: 48,
                            height: 48,
                            child: Image.memory(
                              appWithIcon.icon,
                              width: 48,
                              height: 48,
                            ),
                          );
                        }
                        return Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade300,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(Icons.android, color: Colors.grey.shade600),
                        );
                      },
                    ),
                    title: Text(
                      app.appName,
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: 4),
                        Text(
                          app.packageName,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(
                              canOpen ? Icons.check_circle : Icons.lock,
                              size: 16,
                              color: canOpen ? Colors.green : Colors.red,
                            ),
                            SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                canOpen
                                    ? 'Available to open'
                                    : AppRestrictionService.getTimeUntilNextAccess(app.packageName),
                                style: TextStyle(
                                  fontSize: 13,
                                  color: canOpen ? Colors.green : Colors.red,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    trailing: canOpen
                        ? Container(
                            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: Colors.green.shade100,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              'Ready',
                              style: TextStyle(
                                color: Colors.green.shade800,
                                fontWeight: FontWeight.w600,
                                fontSize: 12,
                              ),
                            ),
                          )
                        : ElevatedButton.icon(
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => PDFReaderScreen(
                                    appPackageName: app.packageName,
                                    appName: app.appName,
                                  ),
                                ),
                              );
                            },
                            icon: Icon(Icons.menu_book, size: 16),
                            label: Text('Read'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                            ),
                          ),
                  ),
                );
              },
            ),
    );
  }
}